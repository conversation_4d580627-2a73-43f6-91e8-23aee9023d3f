# Swaras Academy Portal

## Getting Started

Refer to the [Metronic Vite Documentation](https://docs.keenthemes.com/metronic-react)
for comprehensive guidance on setting up and getting started your project with Metronic.

## ReUI Components

Metronic now leverages [ReUI](https://reui.io), our open-source React component library.

Star the [ReUI on GitHub](https://github.com/keenthemes/reui) to help us grow the project and stay updated on new features!

## Installation

To set up the project dependencies, including those required for React 19, use the `--force` flag to resolve any dependency conflicts:

```bash
npm install --force
```

## Development

Start the development server:

```bash
npm run dev
```

Visit `http://localhost:5173/` to access the application.

## Authentication

The application includes a simple local authentication system for demo purposes:

### Demo Login Credentials
- **Email**: <EMAIL>
- **Password**: demo123

### Features
- Simple login/logout functionality
- Protected routes that require authentication
- User session management with localStorage
- Clean login interface

When you visit the application, you'll be redirected to the login page. Use the demo credentials above to access the dashboard.

### Setting Up the Demo Layout

Follow the [Metronic Vite Documentation](https://docs.keenthemes.com/metronic-vite/guides/layouts) to configure and use the demo layout of your choice.

### Reporting Issues

If you encounter any issues or have suggestions for improvement, please contact us at [<EMAIL>](mailto:<EMAIL>).
Include a detailed description of the issue or suggestion, and we will work to address it in the next stable release.
