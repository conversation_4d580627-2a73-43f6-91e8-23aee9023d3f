// Demo authentication adapter for local authentication
// This provides compatibility with existing code that imports LocalAuthAdapter

export const LocalAuthAdapter = {
  // Demo login function
  async signIn(email, password) {
    // This is handled by the auth context now
    throw new Error('Use the auth context login function instead');
  },

  // Demo password reset function
  async resetPassword(email) {
    console.log('Demo: Password reset requested for:', email);
    // In demo mode, just return success
    return {
      success: true,
      message: 'Demo: Password reset email would be sent'
    };
  },

  // Demo password update function
  async updatePassword(newPassword) {
    console.log('Demo: Password update requested');
    // In demo mode, just return success
    return {
      success: true,
      message: 'Demo: Password would be updated'
    };
  },

  // Demo OAuth function
  async signInWithOAuth(provider, options) {
    console.log('Demo: OAuth not supported in demo mode');
    throw new Error('OAuth authentication is not available in demo mode');
  }
};

// Also export as SupabaseAdapter for compatibility
export const SupabaseAdapter = LocalAuthAdapter;
