# Swaras Academy Portal Environment Variables

# Application Configuration
VITE_APP_NAME="Swaras Academy Portal"
VITE_APP_VERSION="1.0.0"

# Base URL for the application (if deployed in a subdirectory)
# VITE_BASE_URL="/subdirectory"

# API Configuration (if you add backend integration later)
# VITE_API_URL="http://localhost:3000/api"

# Demo Authentication Settings
# These are the default demo credentials used in the local auth system
# You can customize these in your auth provider if needed
VITE_DEMO_EMAIL="<EMAIL>"
VITE_DEMO_PASSWORD="demo123"

# Development Settings
VITE_DEV_MODE=true
