{"name": "metronic-vite", "private": true, "version": "9.2.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint src --fix", "format": "prettier --write .", "preview": "vite preview"}, "dependencies": {"@auth0/auth0-spa-js": "^2.1.3", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@faker-js/faker": "^9.7.0", "@formatjs/intl-pluralrules": "^5.4.4", "@formatjs/intl-relativetimeformat": "^11.4.11", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-aspect-ratio": "^1.1.4", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-collapsible": "^1.1.8", "@radix-ui/react-context-menu": "^2.2.12", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-direction": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-hover-card": "^1.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-menubar": "^1.1.12", "@radix-ui/react-navigation-menu": "^1.2.10", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toast": "^1.2.11", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-toggle-group": "^1.1.7", "@radix-ui/react-tooltip": "^1.2.4", "@remixicon/react": "^4.6.0", "@supabase/supabase-js": "^2.49.8", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.5", "@tanstack/react-query": "^5.75.1", "@tanstack/react-query-devtools": "^5.75.1", "@tanstack/react-table": "^8.21.3", "apexcharts": "4.7.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dompurify": "^3.2.6", "formik": "^2.4.6", "leaflet": "^1.9.4", "lucide-react": "^0.507.0", "mini-svg-data-uri": "^1.4.4", "next-themes": "^0.4.6", "notistack": "^3.0.2", "postcss-preset-env": "^10.1.6", "qs": "^6.14.0", "react": "^19.1.0", "react-apexcharts": "1.7.0", "react-day-picker": "^9.6.7", "react-dom": "^19.1.0", "react-helmet": "^6.1.0", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.56.1", "react-inlinesvg": "^4.2.0", "react-intl": "^7.1.11", "react-leaflet": "^5.0.0", "react-query": "^3.39.3", "react-resizable-panels": "^2.1.9", "react-router": "^7.5.3", "react-router-dom": "^7.5.3", "react-top-loading-bar": "^3.0.2", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "vite-plugin-windicss": "^1.9.4", "yup": "^1.6.1", "zod": "^3.24.3"}, "devDependencies": {"@eslint/js": "^9.22.0", "@ianvs/prettier-plugin-sort-imports": "^4.4.1", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "dotenv": "^16.5.0", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.0.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.1.5", "tsx": "^4.19.4", "vite": "^6.3.4"}}