import { createContext, useContext, useEffect, useState } from 'react';
import { DEMO_CREDENTIALS, DEMO_USER_PROFILE, AUTH_SETTINGS, DemoAuth } from '../local_auth.jsx';

// Auth context
const AuthContext = createContext();

// Auth provider component
export function AuthProvider({ children }) {
  const [auth, setAuth] = useState(null);
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Initialize auth state from localStorage
  useEffect(() => {
    const initAuth = () => {
      try {
        const stored = DemoAuth.getStoredAuth();

        if (stored && DemoAuth.isTokenValid(stored.auth)) {
          setAuth(stored.auth);
          setUser(stored.user);
        } else {
          // Clear expired or invalid auth
          DemoAuth.clearAuth();
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        DemoAuth.clearAuth();
      } finally {
        setLoading(false);
      }
    };

    initAuth();
  }, []);

  // Login function
  const login = async (email, password) => {
    try {
      setLoading(true);

      // Validate demo credentials
      if (!DemoAuth.validateCredentials(email, password)) {
        throw new Error(AUTH_SETTINGS.DEMO_MESSAGES.INVALID_CREDENTIALS);
      }

      // Generate auth token
      const authData = DemoAuth.generateAuthToken();

      // Save auth data
      if (!DemoAuth.saveAuth(authData, DEMO_USER_PROFILE)) {
        throw new Error('Failed to save authentication data');
      }

      // Update state
      setAuth(authData);
      setUser(DEMO_USER_PROFILE);

      return { success: true };
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Logout function
  const logout = () => {
    try {
      // Clear auth data
      DemoAuth.clearAuth();

      // Clear state
      setAuth(null);
      setUser(null);
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  // Verify auth function
  const verify = async () => {
    try {
      const stored = DemoAuth.getStoredAuth();

      if (stored && DemoAuth.isTokenValid(stored.auth)) {
        setAuth(stored.auth);
        setUser(stored.user);
        return { success: true };
      } else {
        // Clear expired or invalid auth
        DemoAuth.clearAuth();
        setAuth(null);
        setUser(null);
      }

      return { success: false };
    } catch (error) {
      console.error('Verify error:', error);
      return { success: false };
    }
  };

  // Save auth function (for compatibility)
  const saveAuth = (authData) => {
    try {
      DemoAuth.saveAuth(authData, user || DEMO_USER_PROFILE);
      setAuth(authData);
    } catch (error) {
      console.error('Save auth error:', error);
    }
  };

  // Set user function (for compatibility)
  const setCurrentUser = (userData) => {
    try {
      DemoAuth.saveAuth(auth || DemoAuth.generateAuthToken(), userData);
      setUser(userData);
    } catch (error) {
      console.error('Set user error:', error);
    }
  };

  const value = {
    auth,
    user,
    loading,
    login,
    logout,
    verify,
    saveAuth,
    setUser: setCurrentUser,
    setLoading,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

// Hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}