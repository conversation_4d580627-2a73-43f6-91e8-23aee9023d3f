import { DropdownMenu4 } from '@/partials/dropdown-menu/dropdown-menu-4';
import {
  RiBankLine,
  RiFacebookCircleLine,
  RiGoogleLine,
  RiInstagramLine,
  RiStore2Line,
} from '@remixicon/react';
import { <PERSON>Down, ArrowUp, EllipsisVertical } from 'lucide-react';
import { Badge, BadgeDot } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const Highlights = ({ limit }) => {
  const rows = [
    {
      icon: RiStore2Line,
      text: 'Online Store',
      total: 172,
      stats: 3.9,
      increase: true,
    },
    {
      icon: RiFacebookCircleLine,
      text: 'Facebook',
      total: 85,
      stats: 0.7,
      increase: false,
    },
    {
      icon: RiInstagramLine,
      text: 'Instagram',
      total: 36,
      stats: 8.2,
      increase: true,
    },
    {
      icon: RiGoogleLine,
      text: 'Google',
      total: 26,
      stats: 8.2,
      increase: true,
    },
    { icon: RiBankLine, text: 'Retail', total: 7, stats: 0.7, increase: false },
  ];

  const items = [
    { badgeColor: 'bg-green-500', label: 'Metronic' },
    { badgeColor: 'bg-destructive', label: 'Bundle' },
    { badgeColor: 'bg-violet-500', label: 'MetronicNest' },
  ];

  const renderRow = (row, index) => {
    return (
      <div
        key={index}
        className="flex items-center justify-between flex-wrap gap-2"
      >
        <div className="flex items-center gap-1.5">
          <row.icon className="size-4.5 text-muted-foreground" />
          <span className="text-sm font-normal text-mono">{row.text}</span>
        </div>
        <div className="flex items-center text-sm font-medium text-foreground gap-6">
          <span className="lg:text-right">${row.total}k</span>
          <span className="flex items-center justify-end gap-1">
            {row.increase ? (
              <ArrowUp className="text-green-500 size-4" />
            ) : (
              <ArrowDown className="text-destructive size-4" />
            )}
            {row.stats}%
          </span>
        </div>
      </div>
    );
  };

  const renderItem = (item, index) => {
    return (
      <div key={index} className="flex items-center gap-1.5">
        <BadgeDot className={item.badgeColor} />
        <span className="text-sm font-normal text-foreground">
          {item.label}
        </span>
      </div>
    );
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle>Highlights</CardTitle>
        <DropdownMenu4
          trigger={
            <Button variant="ghost" mode="icon">
              <EllipsisVertical />
            </Button>
          }
        />
      </CardHeader>
      <CardContent className="flex flex-col gap-4 p-5 lg:p-7.5 lg:pt-4">
        <div className="flex flex-col gap-0.5">
          <span className="text-sm font-normal text-secondary-foreground">
            All time sales
          </span>
          <div className="flex items-center gap-2.5">
            <span className="text-3xl font-semibold text-mono">$295.7k</span>
            <Badge size="sm" variant="success" appearance="outline">
              +2.7%
            </Badge>
          </div>
        </div>
        <div className="flex items-center gap-1 mb-1.5">
          <div className="bg-green-500 h-2 w-full max-w-[60%] rounded-xs"></div>
          <div className="bg-destructive h-2 w-full max-w-[25%] rounded-xs"></div>
          <div className="bg-violet-500 h-2 w-full max-w-[15%] rounded-xs"></div>
        </div>
        <div className="flex items-center flex-wrap gap-4 mb-1">
          {items.map((item, index) => {
            return renderItem(item, index);
          })}
        </div>
        <div className="border-b border-input"></div>
        <div className="grid gap-3">{rows.slice(0, limit).map(renderRow)}</div>
      </CardContent>
    </Card>
  );
};

export { Highlights };
