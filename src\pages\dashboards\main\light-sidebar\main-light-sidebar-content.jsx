import { Fragment } from 'react';
import {
  ChannelStats,
  EntryCallout,
  Highlights,
  TeamMeeting,
  Teams,
  EarningsChart,
} from './components';

const MainLightSidebarContent = () => {
  return (
    <Fragment>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-5 lg:gap-7.5 mb-5 lg:mb-7.5">
        <div className="lg:col-span-1">
          <div className="grid gap-5 lg:gap-7.5">
            <ChannelStats />
            <TeamMeeting />
          </div>
        </div>
        <div className="lg:col-span-2">
          <div className="flex flex-col gap-5 lg:gap-7.5">
            <EarningsChart />
            <EntryCallout />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-3 gap-5 lg:gap-7.5 mb-5 lg:mb-7.5">
        <div className="xl:col-span-1">
          <Highlights />
        </div>
        <div className="xl:col-span-2">
          <Teams />
        </div>
      </div>
    </Fragment>
  );
};

export { MainLightSidebarContent };
