// Local Authentication Configuration
// This file contains all authentication data and settings for demo mode

// Demo credentials - only these will work for login
export const DEMO_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'demo123'
};

// Demo user profile data
export const DEMO_USER_PROFILE = {
  id: 'demo-user-1',
  email: DEMO_CREDENTIALS.email,
  username: 'demo_user',
  first_name: 'De<PERSON>',
  last_name: 'User',
  fullname: 'Demo User',
  avatar: '/media/avatars/300-2.png',
  role: 'admin',
  phone: '******-567-8900',
  company: 'Swaras Academy',
  department: 'Music Education',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  email_verified: true,
  phone_verified: false,
  two_factor_enabled: false,
  preferences: {
    theme: 'light',
    language: 'en',
    notifications: {
      email: true,
      push: true,
      sms: false
    }
  }
};

// Authentication settings
export const AUTH_SETTINGS = {
  // Token expiration time (24 hours in milliseconds)
  TOKEN_EXPIRY: 24 * 60 * 60 * 1000,

  // Session storage keys
  STORAGE_KEYS: {
    AUTH: 'auth',
    USER: 'user',
    PREFERENCES: 'user_preferences'
  },

  // Demo mode messages
  DEMO_MESSAGES: {
    REGISTRATION_DISABLED: 'Registration is not available in demo mode. <NAME_EMAIL> and demo123 to sign in.',
    PASSWORD_RESET_DISABLED: 'Password reset is not available in demo mode. <NAME_EMAIL> with password demo123 to sign in.',
    PASSWORD_CHANGE_DISABLED: 'Password change is not available in demo mode. <NAME_EMAIL> with password demo123 to sign in.',
    OAUTH_DISABLED: 'OAuth authentication is not available in demo mode.',
    TWO_FACTOR_DISABLED: 'Two-factor authentication is not available in demo mode.',
    INVALID_CREDENTIALS: 'Invalid email or password. <NAME_EMAIL> and demo123.',
    LOGIN_SUCCESS: 'Successfully logged in with demo credentials.',
    LOGOUT_SUCCESS: 'Successfully logged out.'
  }
};

// Demo authentication functions
export const DemoAuth = {
  // Validate demo credentials
  validateCredentials(email, password) {
    return email.trim() === DEMO_CREDENTIALS.email && password === DEMO_CREDENTIALS.password;
  },

  // Generate demo auth token
  generateAuthToken() {
    return {
      access_token: `demo_token_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      refresh_token: `demo_refresh_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      expires_at: Date.now() + AUTH_SETTINGS.TOKEN_EXPIRY,
      token_type: 'Bearer',
      issued_at: Date.now()
    };
  },

  // Check if token is valid
  isTokenValid(authData) {
    return authData &&
           authData.access_token &&
           authData.expires_at &&
           authData.expires_at > Date.now();
  },

  // Get stored auth data
  getStoredAuth() {
    try {
      const storedAuth = localStorage.getItem(AUTH_SETTINGS.STORAGE_KEYS.AUTH);
      const storedUser = localStorage.getItem(AUTH_SETTINGS.STORAGE_KEYS.USER);

      if (storedAuth && storedUser) {
        return {
          auth: JSON.parse(storedAuth),
          user: JSON.parse(storedUser)
        };
      }
    } catch (error) {
      console.error('Error reading stored auth:', error);
    }
    return null;
  },

  // Save auth data
  saveAuth(authData, userData) {
    try {
      localStorage.setItem(AUTH_SETTINGS.STORAGE_KEYS.AUTH, JSON.stringify(authData));
      localStorage.setItem(AUTH_SETTINGS.STORAGE_KEYS.USER, JSON.stringify(userData));
      return true;
    } catch (error) {
      console.error('Error saving auth:', error);
      return false;
    }
  },

  // Clear auth data
  clearAuth() {
    try {
      localStorage.removeItem(AUTH_SETTINGS.STORAGE_KEYS.AUTH);
      localStorage.removeItem(AUTH_SETTINGS.STORAGE_KEYS.USER);
      localStorage.removeItem(AUTH_SETTINGS.STORAGE_KEYS.PREFERENCES);
      return true;
    } catch (error) {
      console.error('Error clearing auth:', error);
      return false;
    }
  }
};

// Export default configuration
export default {
  DEMO_CREDENTIALS,
  DEMO_USER_PROFILE,
  AUTH_SETTINGS,
  DemoAuth
};
