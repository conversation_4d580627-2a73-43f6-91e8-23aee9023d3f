import { ChevronFirst } from 'lucide-react';
import { Link } from 'react-router-dom';
import { toAbsoluteUrl } from '@/lib/helpers';
import { cn } from '@/lib/utils';
import { useSettings } from '@/providers/settings-provider';
import { Button } from '@/components/ui/button';

export function SidebarHeader() {
  const { settings, storeOption } = useSettings();

  const handleToggleClick = () => {
    storeOption(
      'layouts.main.sidebarCollapse',
      !settings.layouts.main.sidebarCollapse,
    );
  };

  return (
    <div className="sidebar-header hidden lg:flex items-center relative justify-between px-3 lg:px-6 shrink-0 border-b-1 border-[#ececec7a] pb-2 pt-2">
      <Link to="/">
        <div className="dark:hidden " >
          <img
            src={toAbsoluteUrl('/media/swarasacademy/logo.png')}
            className="default-logo h-[50px] max-w-none"
            alt="Default Logo"
          />

          <img
            src={toAbsoluteUrl('/media/swarasacademy/logo.png')}
            className="small-logo h-[50px] max-w-none"
            alt="Mini Logo"
          />
        </div>
        {/* <div className="hidden dark:block">
          <img
            src={toAbsoluteUrl('/media/app/default-logo-dark.svg')}
            className="default-logo h-[50px] max-w-none"
            alt="Default Dark Logo"
          />

          <img
            src={toAbsoluteUrl('/media/swarasacademy/logo.png')}
            className="small-logo h-[50px] max-w-none"
            alt="Mini Logo"
          />
        </div> */}
      </Link>
      <Button
        onClick={handleToggleClick}
        size="sm"
        mode="icon"
        variant="outline"
        className={cn(
          'size-7 absolute start-full top-2/4 rtl:translate-x-2/4 -translate-x-2/4 -translate-y-2/4',
          settings.layouts.main.sidebarCollapse
            ? 'ltr:rotate-180'
            : 'rtl:rotate-180',
        )}
      >
        <ChevronFirst className="size-4!" />
      </Button>
    </div>
  );
}
